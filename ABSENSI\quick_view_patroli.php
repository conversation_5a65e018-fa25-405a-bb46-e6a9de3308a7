<?php
// Quick view untuk data patroli
require_once 'sw-library/sw-config.php';
?>
<!DOCTYPE html>
<html>
<head>
    <title>Quick View - Data Patroli</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * { box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .content { padding: 30px; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        .stat-number { font-size: 2em; font-weight: bold; color: #667eea; }
        .stat-label { color: #666; margin-top: 5px; }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin: 20px 0; 
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        th { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 15px; 
            text-align: left; 
            font-weight: 600;
        }
        td { 
            padding: 12px 15px; 
            border-bottom: 1px solid #eee; 
        }
        tr:hover { background: #f8f9fa; }
        .status { 
            padding: 5px 10px; 
            border-radius: 20px; 
            font-size: 0.85em; 
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .status-completed { background: #d1ecf1; color: #0c5460; }
        .rating { color: #ffc107; font-size: 1.2em; }
        .btn { 
            background: #667eea; 
            color: white; 
            padding: 10px 20px; 
            text-decoration: none; 
            border-radius: 25px; 
            display: inline-block; 
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover { 
            background: #764ba2; 
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .alert { 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 10px; 
            border-left: 4px solid;
        }
        .alert-info { background: #e7f3ff; border-color: #2196F3; color: #0c5460; }
        .alert-success { background: #e8f5e8; border-color: #4CAF50; color: #155724; }
        .alert-warning { background: #fff8e1; border-color: #FF9800; color: #856404; }
        .alert-danger { background: #ffebee; border-color: #f44336; color: #721c24; }
        .no-data { 
            text-align: center; 
            padding: 60px 20px; 
            color: #666; 
        }
        .no-data i { 
            font-size: 4em; 
            color: #ddd; 
            margin-bottom: 20px; 
        }
        @media (max-width: 768px) {
            .container { margin: 10px; border-radius: 10px; }
            .header { padding: 20px; }
            .header h1 { font-size: 2em; }
            .content { padding: 20px; }
            table { font-size: 0.9em; }
            th, td { padding: 8px; }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> Data Patroli</h1>
            <p>Sistem Monitoring Patroli Keamanan</p>
        </div>
        
        <div class="content">
            <?php
            // Check if table exists
            $check_table = "SHOW TABLES LIKE 'patroli'";
            $table_exists = $connection->query($check_table);
            
            if(!$table_exists || $table_exists->num_rows == 0) {
                echo "<div class='alert alert-warning'>
                        <h3><i class='fas fa-exclamation-triangle'></i> Tabel Patroli Belum Ada</h3>
                        <p>Tabel 'patroli' belum dibuat di database. Silakan buat tabel terlebih dahulu melalui admin panel.</p>
                        <a href='sw-admin/' class='btn'><i class='fas fa-cog'></i> Ke Admin Panel</a>
                      </div>";
            } else {
                // Get statistics
                $stats_query = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                    SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    AVG(rating) as avg_rating
                    FROM patroli";
                
                $stats_result = $connection->query($stats_query);
                $stats = $stats_result->fetch_assoc();
                
                if($stats['total'] == 0) {
                    echo "<div class='no-data'>
                            <i class='fas fa-clipboard-list'></i>
                            <h3>Belum Ada Data Patroli</h3>
                            <p>Tabel patroli sudah siap, tapi belum ada data yang tercatat.</p>
                            <a href='sw-admin/' class='btn'><i class='fas fa-plus'></i> Tambah Data Patroli</a>
                          </div>";
                } else {
                    // Show statistics
                    echo "<div class='stats'>
                            <div class='stat-card'>
                                <div class='stat-number'>{$stats['total']}</div>
                                <div class='stat-label'>Total Patroli</div>
                            </div>
                            <div class='stat-card'>
                                <div class='stat-number'>{$stats['pending']}</div>
                                <div class='stat-label'>Menunggu</div>
                            </div>
                            <div class='stat-card'>
                                <div class='stat-number'>{$stats['approved']}</div>
                                <div class='stat-label'>Disetujui</div>
                            </div>
                            <div class='stat-card'>
                                <div class='stat-number'>{$stats['completed']}</div>
                                <div class='stat-label'>Selesai</div>
                            </div>
                            <div class='stat-card'>
                                <div class='stat-number'>" . number_format($stats['avg_rating'], 1) . "</div>
                                <div class='stat-label'>Rata-rata Rating</div>
                            </div>
                          </div>";
                    
                    // Get data
                    $query = "SELECT 
                        p.patroli_id,
                        e.employees_name,
                        b.name as building_name,
                        p.dokumentasi,
                        p.tanggal,
                        p.status,
                        p.rating,
                        p.komentar
                        FROM patroli p
                        LEFT JOIN employees e ON p.employees_id = e.id
                        LEFT JOIN building b ON p.building_id = b.building_id
                        ORDER BY p.tanggal DESC
                        LIMIT 20";
                    
                    $result = $connection->query($query);
                    
                    if($result && $result->num_rows > 0) {
                        echo "<table>
                                <thead>
                                    <tr>
                                        <th><i class='fas fa-hashtag'></i> ID</th>
                                        <th><i class='fas fa-user'></i> Karyawan</th>
                                        <th><i class='fas fa-building'></i> Lokasi</th>
                                        <th><i class='fas fa-calendar'></i> Tanggal</th>
                                        <th><i class='fas fa-flag'></i> Status</th>
                                        <th><i class='fas fa-star'></i> Rating</th>
                                        <th><i class='fas fa-comment'></i> Komentar</th>
                                    </tr>
                                </thead>
                                <tbody>";
                        
                        while($row = $result->fetch_assoc()) {
                            $tanggal = date('d/m/Y H:i', strtotime($row['tanggal']));
                            
                            // Status
                            $status_class = 'status-' . $row['status'];
                            $status_text = '';
                            switch($row['status']) {
                                case 'pending': $status_text = 'Menunggu'; break;
                                case 'approved': $status_text = 'Disetujui'; break;
                                case 'rejected': $status_text = 'Ditolak'; break;
                                case 'completed': $status_text = 'Selesai'; break;
                                default: $status_text = $row['status']; break;
                            }
                            
                            // Rating
                            $rating_display = '';
                            if($row['rating']) {
                                for($i = 1; $i <= 5; $i++) {
                                    if($i <= $row['rating']) {
                                        $rating_display .= '<span class="rating">★</span>';
                                    } else {
                                        $rating_display .= '☆';
                                    }
                                }
                                $rating_display .= ' (' . $row['rating'] . '/5)';
                            } else {
                                $rating_display = '<span style="color: #999;">Belum dinilai</span>';
                            }
                            
                            // Komentar
                            $komentar = $row['komentar'] ? 
                                (strlen($row['komentar']) > 60 ? 
                                    substr($row['komentar'], 0, 60) . '...' : 
                                    $row['komentar']) : 
                                '<span style="color: #999;">-</span>';
                            
                            echo "<tr>
                                    <td><strong>{$row['patroli_id']}</strong></td>
                                    <td>{$row['employees_name']}</td>
                                    <td>{$row['building_name']}</td>
                                    <td>{$tanggal}</td>
                                    <td><span class='{$status_class}'>{$status_text}</span></td>
                                    <td>{$rating_display}</td>
                                    <td title='{$row['komentar']}'>{$komentar}</td>
                                  </tr>";
                        }
                        
                        echo "</tbody></table>";
                        
                        if($stats['total'] > 20) {
                            echo "<div class='alert alert-info'>
                                    <p><i class='fas fa-info-circle'></i> Menampilkan 20 data terbaru dari total {$stats['total']} data patroli.</p>
                                  </div>";
                        }
                    }
                }
            }
            ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="sw-admin/" class="btn"><i class="fas fa-cog"></i> Admin Panel</a>
                <a href="show_patroli_data.php" class="btn"><i class="fas fa-table"></i> View Lengkap</a>
                <a href="javascript:window.location.reload()" class="btn"><i class="fas fa-sync"></i> Refresh</a>
            </div>
            
            <div style="text-align: center; margin-top: 20px; color: #666; font-size: 0.9em;">
                <p><i class="fas fa-clock"></i> Generated: <?php echo date('d/m/Y H:i:s'); ?> | Database: <?php echo $DB_NAME; ?></p>
            </div>
        </div>
    </div>
</body>
</html>
