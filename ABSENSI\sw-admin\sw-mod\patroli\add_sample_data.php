<?php
// AJAX handler untuk menambah data contoh patroli
session_start();
header('Content-Type: application/json');

if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    echo json_encode(['success' => false, 'message' => 'Aks<PERSON> ditolak! Silakan login terlebih dahulu.']);
    exit;
}

require_once'../../../sw-library/sw-config.php';
require_once'../../login/login_session.php';

// Check if user is admin or operator
if($level_user != 1 && $level_user != 2) {
    echo json_encode(['success' => false, 'message' => 'Akses ditolak! Hanya admin dan operator yang dapat menambah data.']);
    exit;
}

try {
    // Check if table exists
    $check_table = "SHOW TABLES LIKE 'patroli'";
    $table_exists = $connection->query($check_table);
    
    if(!$table_exists || $table_exists->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'Tabel patroli belum ada! <PERSON><PERSON><PERSON> buat tabel terlebih dahulu.']);
        exit;
    }
    
    // Get employees and buildings
    $employees = [];
    $buildings = [];
    
    $emp_result = $connection->query("SELECT id, employees_name FROM employees LIMIT 5");
    if($emp_result) {
        while($row = $emp_result->fetch_assoc()) {
            $employees[] = $row;
        }
    }
    
    $building_result = $connection->query("SELECT building_id, name FROM building LIMIT 3");
    if($building_result) {
        while($row = $building_result->fetch_assoc()) {
            $buildings[] = $row;
        }
    }
    
    if(empty($employees) || empty($buildings)) {
        echo json_encode(['success' => false, 'message' => 'Tidak ada data karyawan atau lokasi! Silakan tambahkan data master terlebih dahulu.']);
        exit;
    }
    
    // Sample data
    $sample_data = [
        [
            'employees_id' => $employees[0]['id'],
            'building_id' => $buildings[0]['building_id'],
            'dokumentasi' => 'patroli-sample-1-' . time() . '.jpg',
            'status' => 'completed',
            'rating' => 5,
            'komentar' => 'Patroli rutin pagi hari berjalan lancar. Area aman dan bersih, semua peralatan dalam kondisi baik.',
            'tanggal' => date('Y-m-d H:i:s', strtotime('-2 hours'))
        ],
        [
            'employees_id' => $employees[1]['id'] ?? $employees[0]['id'],
            'building_id' => $buildings[1]['building_id'] ?? $buildings[0]['building_id'],
            'dokumentasi' => 'patroli-sample-2-' . time() . '.jpg',
            'status' => 'approved',
            'rating' => 4,
            'komentar' => 'Patroli keamanan malam. Ditemukan beberapa lampu mati di area parkir, sudah dilaporkan ke maintenance.',
            'tanggal' => date('Y-m-d H:i:s', strtotime('-1 hour'))
        ],
        [
            'employees_id' => $employees[2]['id'] ?? $employees[0]['id'],
            'building_id' => $buildings[0]['building_id'],
            'dokumentasi' => 'patroli-sample-3-' . time() . '.jpg',
            'status' => 'pending',
            'rating' => null,
            'komentar' => 'Patroli siang hari. Menunggu verifikasi dari supervisor untuk area yang perlu perhatian khusus.',
            'tanggal' => date('Y-m-d H:i:s', strtotime('-30 minutes'))
        ],
        [
            'employees_id' => $employees[0]['id'],
            'building_id' => $buildings[2]['building_id'] ?? $buildings[0]['building_id'],
            'dokumentasi' => 'patroli-sample-4-' . time() . '.jpg',
            'status' => 'rejected',
            'rating' => 2,
            'komentar' => 'Dokumentasi kurang lengkap. Perlu patroli ulang dengan dokumentasi yang lebih detail.',
            'tanggal' => date('Y-m-d H:i:s', strtotime('-3 hours'))
        ],
        [
            'employees_id' => $employees[1]['id'] ?? $employees[0]['id'],
            'building_id' => $buildings[1]['building_id'] ?? $buildings[0]['building_id'],
            'dokumentasi' => 'patroli-sample-5-' . time() . '.jpg',
            'status' => 'completed',
            'rating' => 3,
            'komentar' => 'Patroli kebersihan. Area cukup bersih namun perlu perhatian khusus di area toilet dan pantry.',
            'tanggal' => date('Y-m-d H:i:s', strtotime('-10 minutes'))
        ]
    ];
    
    $success_count = 0;
    $latest_id = 0;
    
    foreach($sample_data as $data) {
        $rating_value = ($data['rating'] !== null) ? "'{$data['rating']}'" : "NULL";
        $komentar_value = "'" . $connection->real_escape_string($data['komentar']) . "'";
        
        $query = "INSERT INTO patroli (employees_id, building_id, dokumentasi, tanggal, status, rating, komentar) 
                  VALUES ('{$data['employees_id']}', '{$data['building_id']}', '{$data['dokumentasi']}', 
                          '{$data['tanggal']}', '{$data['status']}', $rating_value, $komentar_value)";
        
        if($connection->query($query)) {
            $latest_id = $connection->insert_id;
            $success_count++;
        }
    }
    
    if($success_count > 0) {
        echo json_encode([
            'success' => true, 
            'message' => "Berhasil menambahkan $success_count data contoh patroli! Data akan ditampilkan di tabel.",
            'count' => $success_count,
            'latest_id' => $latest_id
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Gagal menambahkan data contoh!']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
