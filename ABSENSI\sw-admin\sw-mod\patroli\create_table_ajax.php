<?php
// AJAX handler untuk membuat tabel patroli
session_start();
header('Content-Type: application/json');

if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    echo json_encode(['success' => false, 'message' => 'Aks<PERSON> ditolak! <PERSON>lakan login terlebih dahulu.']);
    exit;
}

require_once'../../../sw-library/sw-config.php';
require_once'../../login/login_session.php';

// Check if user is admin or operator
if($level_user != 1 && $level_user != 2) {
    echo json_encode(['success' => false, 'message' => 'Akses ditolak! Hanya admin dan operator yang dapat membuat tabel.']);
    exit;
}

try {
    // Check if table already exists
    $check_table = "SHOW TABLES LIKE 'patroli'";
    $table_exists = $connection->query($check_table);
    
    if($table_exists && $table_exists->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'Tabel patroli sudah ada!']);
        exit;
    }
    
    // Create patroli table
    $create_sql = "
    CREATE TABLE `patroli` (
      `patroli_id` int(11) NOT NULL AUTO_INCREMENT,
      `employees_id` int(11) NOT NULL COMMENT 'ID karyawan yang melakukan patroli',
      `building_id` int(11) NOT NULL COMMENT 'ID lokasi patroli',
      `dokumentasi` varchar(200) NOT NULL COMMENT 'File foto/video dokumentasi patroli',
      `tanggal` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Tanggal dan waktu patroli dilakukan',
      `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT 'Status patroli: pending, approved, rejected, completed',
      `komentar` text NULL COMMENT 'Komentar dari operator atau karyawan',
      `rating` int(1) NULL COMMENT 'Rating patroli 1-5, null jika belum dinilai',
      `checklist_id` int(11) NULL COMMENT 'ID checklist untuk bagian operator',
      PRIMARY KEY (`patroli_id`),
      KEY `idx_employees_id` (`employees_id`),
      KEY `idx_building_id` (`building_id`),
      KEY `idx_tanggal` (`tanggal`),
      KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    
    if(!$connection->query($create_sql)) {
        echo json_encode(['success' => false, 'message' => 'Gagal membuat tabel: ' . $connection->error]);
        exit;
    }
    
    // Add foreign key constraints (optional, ignore errors if tables don't exist)
    $fk_sql = "
    ALTER TABLE `patroli`
      ADD CONSTRAINT `fk_patroli_employees` FOREIGN KEY (`employees_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
      ADD CONSTRAINT `fk_patroli_building` FOREIGN KEY (`building_id`) REFERENCES `building` (`building_id`) ON DELETE CASCADE ON UPDATE CASCADE;
    ";
    
    // Execute FK constraints (ignore errors)
    $connection->query($fk_sql);
    
    // Create upload directory
    $upload_dir = '../../../sw-content/patroli/';
    if(!is_dir($upload_dir)) {
        if(mkdir($upload_dir, 0755, true)) {
            // Create index.html for security
            $index_content = '<!DOCTYPE html>
<html>
<head>
    <title>403 Forbidden</title>
</head>
<body>
    <h1>Directory access is forbidden.</h1>
</body>
</html>';
            file_put_contents($upload_dir . 'index.html', $index_content);
        }
    }
    
    // Insert sample data
    $sample_data = [];
    
    // Get some employees and buildings for sample data
    $emp_result = $connection->query("SELECT id FROM employees LIMIT 3");
    $building_result = $connection->query("SELECT building_id FROM building LIMIT 2");
    
    $employees = [];
    $buildings = [];
    
    if($emp_result) {
        while($row = $emp_result->fetch_assoc()) {
            $employees[] = $row['id'];
        }
    }
    
    if($building_result) {
        while($row = $building_result->fetch_assoc()) {
            $buildings[] = $row['building_id'];
        }
    }
    
    // Add sample data if we have employees and buildings
    if(!empty($employees) && !empty($buildings)) {
        $sample_data = [
            [
                'employees_id' => $employees[0],
                'building_id' => $buildings[0],
                'dokumentasi' => 'sample-patroli-1.jpg',
                'status' => 'completed',
                'rating' => 5,
                'komentar' => 'Patroli rutin pagi hari berjalan lancar. Area aman dan bersih.',
                'tanggal' => date('Y-m-d H:i:s', strtotime('-2 hours'))
            ],
            [
                'employees_id' => $employees[1] ?? $employees[0],
                'building_id' => $buildings[1] ?? $buildings[0],
                'dokumentasi' => 'sample-patroli-2.jpg',
                'status' => 'pending',
                'rating' => null,
                'komentar' => 'Patroli siang hari. Menunggu verifikasi supervisor.',
                'tanggal' => date('Y-m-d H:i:s', strtotime('-30 minutes'))
            ],
            [
                'employees_id' => $employees[2] ?? $employees[0],
                'building_id' => $buildings[0],
                'dokumentasi' => 'sample-patroli-3.jpg',
                'status' => 'approved',
                'rating' => 4,
                'komentar' => 'Patroli keamanan malam. Semua area dalam kondisi baik.',
                'tanggal' => date('Y-m-d H:i:s', strtotime('-1 hour'))
            ]
        ];
        
        foreach($sample_data as $data) {
            $rating_value = ($data['rating'] !== null) ? "'{$data['rating']}'" : "NULL";
            $komentar_value = "'" . $connection->real_escape_string($data['komentar']) . "'";
            
            $insert_sql = "INSERT INTO patroli (employees_id, building_id, dokumentasi, tanggal, status, rating, komentar) 
                          VALUES ('{$data['employees_id']}', '{$data['building_id']}', '{$data['dokumentasi']}', 
                                  '{$data['tanggal']}', '{$data['status']}', $rating_value, $komentar_value)";
            
            $connection->query($insert_sql);
        }
    }
    
    $message = 'Tabel patroli berhasil dibuat!';
    if(!empty($sample_data)) {
        $message .= ' Ditambahkan ' . count($sample_data) . ' data contoh.';
    }
    
    echo json_encode([
        'success' => true, 
        'message' => $message,
        'sample_count' => count($sample_data)
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
