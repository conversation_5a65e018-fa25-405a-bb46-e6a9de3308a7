<?php
// <PERSON>ript sederhana untuk menampilkan data patroli
require_once 'sw-library/sw-config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Data Patroli</title>
    <meta charset='utf-8'>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
        tr:nth-child(even) { background: #f9f9f9; }
        .status-pending { color: #856404; background: #fff3cd; padding: 2px 6px; border-radius: 3px; }
        .status-approved { color: #155724; background: #d4edda; padding: 2px 6px; border-radius: 3px; }
        .status-rejected { color: #721c24; background: #f8d7da; padding: 2px 6px; border-radius: 3px; }
        .status-completed { color: #0c5460; background: #d1ecf1; padding: 2px 6px; border-radius: 3px; }
        .rating { color: #ffc107; }
        .btn { background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px; }
        .alert { padding: 15px; margin: 20px 0; border-radius: 4px; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>";

echo "<h1>📊 Data Patroli - Database</h1>";

// Check if table exists
$check_table = "SHOW TABLES LIKE 'patroli'";
$table_exists = $connection->query($check_table);

if(!$table_exists || $table_exists->num_rows == 0) {
    echo "<div class='alert alert-warning'>
            <h3>⚠️ Tabel Patroli Belum Ada</h3>
            <p>Tabel 'patroli' belum dibuat di database. Silakan buat tabel terlebih dahulu melalui admin panel.</p>
            <a href='sw-admin/' class='btn'>Ke Admin Panel</a>
          </div>";
} else {
    // Get data count
    $count_result = $connection->query("SELECT COUNT(*) as total FROM patroli");
    $total_data = $count_result->fetch_assoc()['total'];
    
    if($total_data == 0) {
        echo "<div class='alert alert-info'>
                <h3>📝 Belum Ada Data</h3>
                <p>Tabel patroli sudah ada tapi belum ada data yang tercatat.</p>
                <a href='sw-admin/' class='btn'>Ke Admin Panel</a>
              </div>";
    } else {
        echo "<div class='alert alert-success'>
                <h3>✅ Data Ditemukan</h3>
                <p>Menampilkan <strong>$total_data</strong> data patroli dari database.</p>
              </div>";
        
        // Get data with joins
        $query = "SELECT 
            p.patroli_id,
            e.employees_name,
            b.name as building_name,
            p.dokumentasi,
            p.tanggal,
            p.status,
            p.rating,
            p.komentar
            FROM patroli p
            LEFT JOIN employees e ON p.employees_id = e.id
            LEFT JOIN building b ON p.building_id = b.building_id
            ORDER BY p.tanggal DESC";
        
        $result = $connection->query($query);
        
        if($result && $result->num_rows > 0) {
            echo "<table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Karyawan</th>
                            <th>Lokasi</th>
                            <th>Tanggal</th>
                            <th>Status</th>
                            <th>Rating</th>
                            <th>Dokumentasi</th>
                            <th>Komentar</th>
                        </tr>
                    </thead>
                    <tbody>";
            
            while($row = $result->fetch_assoc()) {
                $tanggal = date('d/m/Y H:i', strtotime($row['tanggal']));
                
                // Status
                $status_class = 'status-' . $row['status'];
                $status_text = '';
                switch($row['status']) {
                    case 'pending': $status_text = 'Menunggu'; break;
                    case 'approved': $status_text = 'Disetujui'; break;
                    case 'rejected': $status_text = 'Ditolak'; break;
                    case 'completed': $status_text = 'Selesai'; break;
                    default: $status_text = $row['status']; break;
                }
                
                // Rating
                $rating_display = '';
                if($row['rating']) {
                    for($i = 1; $i <= 5; $i++) {
                        if($i <= $row['rating']) {
                            $rating_display .= '<span class="rating">⭐</span>';
                        } else {
                            $rating_display .= '☆';
                        }
                    }
                    $rating_display .= ' (' . $row['rating'] . '/5)';
                } else {
                    $rating_display = '<span style="color: #999;">Belum dinilai</span>';
                }
                
                // Komentar
                $komentar = $row['komentar'] ? 
                    (strlen($row['komentar']) > 50 ? 
                        substr($row['komentar'], 0, 50) . '...' : 
                        $row['komentar']) : 
                    '<span style="color: #999;">-</span>';
                
                echo "<tr>
                        <td>{$row['patroli_id']}</td>
                        <td>{$row['employees_name']}</td>
                        <td>{$row['building_name']}</td>
                        <td>{$tanggal}</td>
                        <td><span class='{$status_class}'>{$status_text}</span></td>
                        <td>{$rating_display}</td>
                        <td>{$row['dokumentasi']}</td>
                        <td title='{$row['komentar']}'>{$komentar}</td>
                      </tr>";
            }
            
            echo "</tbody></table>";
            
            // Statistics
            $stats_result = $connection->query("
                SELECT 
                    status,
                    COUNT(*) as jumlah
                FROM patroli 
                GROUP BY status
            ");
            
            echo "<h3>📈 Statistik Status</h3>";
            echo "<table style='width: auto;'>";
            echo "<tr><th>Status</th><th>Jumlah</th></tr>";
            while($row = $stats_result->fetch_assoc()) {
                $status_text = '';
                switch($row['status']) {
                    case 'pending': $status_text = 'Menunggu'; break;
                    case 'approved': $status_text = 'Disetujui'; break;
                    case 'rejected': $status_text = 'Ditolak'; break;
                    case 'completed': $status_text = 'Selesai'; break;
                    default: $status_text = $row['status']; break;
                }
                echo "<tr><td>{$status_text}</td><td>{$row['jumlah']}</td></tr>";
            }
            echo "</table>";
            
        } else {
            echo "<div class='alert alert-danger'>
                    <h3>❌ Error Query</h3>
                    <p>Terjadi kesalahan saat mengambil data: " . $connection->error . "</p>
                  </div>";
        }
    }
}

echo "<hr>";
echo "<h3>🔗 Link Berguna</h3>";
echo "<a href='sw-admin/' class='btn'>Admin Panel</a>";
echo "<a href='sw-admin/sw-mod/patroli/' class='btn'>Kelola Patroli</a>";

echo "<p style='margin-top: 30px; color: #666; font-size: 12px;'>";
echo "Generated: " . date('d/m/Y H:i:s') . " | Database: {$DB_NAME}";
echo "</p>";

echo "</body></html>";
?>
