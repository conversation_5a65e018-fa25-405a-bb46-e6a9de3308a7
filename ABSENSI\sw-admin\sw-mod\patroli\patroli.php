<?php
if(empty($connection)){
  header('location:../../');
} else {
  include_once 'sw-mod/sw-panel.php';
echo'
  <div class="content-wrapper">';
switch(@$_GET['op']){ 
    default:
echo'
<section class="content-header">
  <h1>Data<small> Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li class="active">Data Patroli</li>
    </ol>
</section>';
// Show success message if any
$success_message = '';
$highlight_id = '';
if(isset($_GET['success']) && isset($_GET['id'])) {
  $success_type = $_GET['success'];
  $highlight_id = $_GET['id'];

  switch($success_type) {
    case 'add':
      $success_message = '<div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
        <h4><i class="icon fa fa-check"></i> Berhasil!</h4>
        Data patroli baru berhasil ditambahkan dengan ID: ' . $highlight_id . '
      </div>';
      break;
    case 'edit':
      $success_message = '<div class="alert alert-info alert-dismissible">
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
        <h4><i class="icon fa fa-info"></i> Berhasil!</h4>
        Data patroli ID: ' . $highlight_id . ' berhasil diupdate
      </div>';
      break;
    case 'delete':
      $success_message = '<div class="alert alert-warning alert-dismissible">
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
        <h4><i class="icon fa fa-warning"></i> Berhasil!</h4>
        Data patroli ID: ' . $highlight_id . ' berhasil dihapus
      </div>';
      break;
    case 'demo':
      $success_message = '<div class="alert alert-info alert-dismissible">
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
        <h4><i class="icon fa fa-info"></i> Demo Data Berhasil!</h4>
        Data demo patroli berhasil ditambahkan. Data terbaru akan di-highlight dengan warna hijau.
      </div>';
      break;
  }
}

echo'
<section class="content">
  <div class="row">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">';

if($success_message) {
  echo $success_message;
}

echo'
      <div class="box box-solid">
        <div class="box-header with-border">
          <h3 class="box-title"><b>Data Patroli</b> <small id="data-count"></small></h3>
          <div class="box-tools pull-right">';
          if($level_user==1 || $level_user==2){
            echo'
            <a href="'.$mod.'&op=add" class="btn btn-success btn-flat"><i class="fa fa-plus"></i> Tambah Baru</a>';}
          else{
            echo'<button type="button" class="btn btn-success btn-flat access-failed"><i class="fa fa-plus"></i> Tambah Baru</button>';
          }echo'
          </div>
        </div>
    <div class="box-body">
      <div class="table-responsive">
          <table id="sw-datatable" class="table table-bordered">
            <thead>
            <tr>
              <th style="width: 10px">No</th>
              <th>Karyawan</th>
              <th>Lokasi</th>
              <th>Tanggal</th>
              <th>Status</th>
              <th style="width: 120px" class="text-center">Rating</th>
              <th>Dokumentasi</th>
              <th style="width:150px" class="text-center">Aksi</th>
            </tr>
            </thead>
            <tbody>';

            // Check if patroli table exists
            $check_table = "SHOW TABLES LIKE 'patroli'";
            $table_exists = $connection->query($check_table);

            if(!$table_exists || $table_exists->num_rows == 0) {
              echo '<tr><td colspan="8" class="text-center">
                      <div class="alert alert-warning" style="margin: 20px;">
                        <h4><i class="fa fa-warning"></i> Tabel Patroli Belum Ada!</h4>
                        <p>Tabel patroli belum dibuat di database. Silakan buat tabel terlebih dahulu.</p>
                        <div class="btn-group">
                          <a href="javascript:void(0)" onclick="createPatroliTable()" class="btn btn-primary btn-lg">
                            <i class="fa fa-database"></i> Buat Tabel Patroli
                          </a>
                        </div>
                      </div>
                    </td></tr>';
            } else {
              // Query with priority for highlighted data
              if($highlight_id) {
                $query ="SELECT p.*, e.employees_name, b.name as building_name,
                         CASE WHEN p.patroli_id = '$highlight_id' THEN 0 ELSE 1 END as sort_priority
                         FROM patroli p
                         LEFT JOIN employees e ON p.employees_id = e.id
                         LEFT JOIN building b ON p.building_id = b.building_id
                         ORDER BY sort_priority ASC, p.tanggal DESC";
              } else {
                $query ="SELECT p.*, e.employees_name, b.name as building_name
                         FROM patroli p
                         LEFT JOIN employees e ON p.employees_id = e.id
                         LEFT JOIN building b ON p.building_id = b.building_id
                         ORDER BY p.tanggal DESC";
              }
              $result = $connection->query($query);

              if(!$result) {
                echo '<tr><td colspan="8" class="text-center">
                        <div class="alert alert-danger">
                          <h4><i class="fa fa-exclamation-triangle"></i> Error Database!</h4>
                          <p>Terjadi kesalahan saat mengambil data: ' . $connection->error . '</p>
                        </div>
                      </td></tr>';
              } elseif($result->num_rows > 0){
              $no=0;
              while ($row = $result->fetch_assoc()) {
              $no++;
              $patroli_id = $row['patroli_id'];
              $employees_name = $row['employees_name'];
              $building_name = $row['building_name'];
              $tanggal = date('d-m-Y H:i', strtotime($row['tanggal']));
              $status = $row['status'];
              $rating = $row['rating'] ? $row['rating'] : '-';
              $dokumentasi = $row['dokumentasi'];
              
              // Status badge
              $status_badge = '';
              switch($status) {
                case 'pending':
                  $status_badge = '<span class="label label-warning">Menunggu</span>';
                  break;
                case 'approved':
                  $status_badge = '<span class="label label-success">Disetujui</span>';
                  break;
                case 'rejected':
                  $status_badge = '<span class="label label-danger">Ditolak</span>';
                  break;
                case 'completed':
                  $status_badge = '<span class="label label-info">Selesai</span>';
                  break;
                default:
                  $status_badge = '<span class="label label-default">'.$status.'</span>';
              }
              
              // Add highlight class for newly added/edited data
              $row_class = '';
              if($highlight_id && $patroli_id == $highlight_id) {
                $row_class = ' class="success"';
              }

              echo'
              <tr'.$row_class.'>
                <td>'.$no.'</td>
                <td>'.$employees_name.'</td>
                <td>'.$building_name.'</td>
                <td>'.$tanggal.'</td>
                <td>'.$status_badge.'</td>
                <td class="text-center">';
                if($rating != '-') {
                  echo '<div class="rating-display">';
                  for($i = 1; $i <= 5; $i++) {
                    if($i <= $rating) {
                      echo '<i class="fa fa-star" style="color: #f39c12;"></i>';
                    } else {
                      echo '<i class="fa fa-star-o" style="color: #ddd;"></i>';
                    }
                  }
                  echo '<br><small class="text-muted">('.$rating.'/5)</small>';
                  echo '</div>';
                } else {
                  echo '<span class="text-muted">Belum dinilai</span>';
                }
                echo'</td>
                <td>';
                if(!empty($dokumentasi)) {
                  // Check if file exists
                  $file_path = '../sw-content/patroli/'.$dokumentasi;
                  if(file_exists($file_path)) {
                    echo '<a href="../sw-content/patroli/'.$dokumentasi.'" target="_blank" class="btn btn-xs btn-success" title="Klik untuk melihat file">
                          <i class="fa fa-eye"></i> Lihat
                        </a><br>
                        <small class="text-muted">'.$dokumentasi.'</small>';
                  } else {
                    echo '<span class="btn btn-xs btn-warning disabled" title="File tidak ditemukan">
                          <i class="fa fa-exclamation-triangle"></i> Missing
                        </span><br>
                        <small class="text-muted">'.$dokumentasi.'</small>';
                  }
                } else {
                  echo '<span class="text-muted">Tidak ada file</span>';
                }
                echo'</td>
                <td class="text-center">';
                  echo'<div class="btn-group">
                    <a href="'.$mod.'&op=edit&patroli_id='.$patroli_id.'" class="btn btn-warning btn-xs" title="Edit">
                      <i class="fa fa-edit"></i>
                    </a>';
                    if($level_user==1 || $level_user==2){
                      echo'<a href="'.$mod.'&op=delete&patroli_id='.$patroli_id.'" class="btn btn-danger btn-xs" title="Hapus" onclick="return confirm(\'Yakin ingin menghapus data ini?\')">
                        <i class="fa fa-trash"></i>
                      </a>';
                    }
                  echo'</div>
                </td>
              </tr>';
              }
                } else {
                  echo '<tr><td colspan="8" class="text-center">
                          <div class="alert alert-info" style="margin: 20px;">
                            <h4><i class="fa fa-info-circle"></i> Belum Ada Data Patroli</h4>
                            <p>Belum ada data patroli yang tercatat. Silakan tambah data baru untuk memulai.</p>
                            <div class="btn-group">
                              <a href="'.$mod.'&op=add" class="btn btn-primary">
                                <i class="fa fa-plus"></i> Tambah Data Baru
                              </a>
                              <button onclick="addSampleData()" class="btn btn-info">
                                <i class="fa fa-database"></i> Tambah Data Contoh
                              </button>
                            </div>
                          </div>
                        </td></tr>';
                }
              }
            }
echo'
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
</section>';
break;

case 'add':
echo'
<section class="content-header">
  <h1>Tambah<small> Data Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li><a href="./'.$mod.'">Data Patroli</a></li>
      <li class="active">Tambah Data</li>
    </ol>
</section>

<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-primary">
        <div class="box-header with-border">
          <h3 class="box-title">Form Tambah Patroli</h3>
        </div>
        <form class="form-horizontal" method="POST" action="sw-mod/'.$mod.'/proses.php?action=add" enctype="multipart/form-data">
          <div class="box-body">
            
            <div class="form-group">
              <label for="employees_id" class="col-sm-2 control-label">Karyawan</label>
              <div class="col-sm-10">
                <select class="form-control" name="employees_id" required>
                  <option value="">-- Pilih Karyawan --</option>';
                  $query_emp = "SELECT id, employees_name FROM employees ORDER BY employees_name";
                  $result_emp = $connection->query($query_emp);
                  while($row_emp = $result_emp->fetch_assoc()) {
                    echo '<option value="'.$row_emp['id'].'">'.$row_emp['employees_name'].'</option>';
                  }
                echo'</select>
              </div>
            </div>

            <div class="form-group">
              <label for="building_id" class="col-sm-2 control-label">Lokasi</label>
              <div class="col-sm-10">
                <select class="form-control" name="building_id" required>
                  <option value="">-- Pilih Lokasi --</option>';
                  $query_building = "SELECT building_id, name FROM building ORDER BY name";
                  $result_building = $connection->query($query_building);
                  while($row_building = $result_building->fetch_assoc()) {
                    echo '<option value="'.$row_building['building_id'].'">'.$row_building['name'].'</option>';
                  }
                echo'</select>
              </div>
            </div>

            <div class="form-group">
              <label for="dokumentasi" class="col-sm-2 control-label">Dokumentasi</label>
              <div class="col-sm-10">
                <input type="file" class="form-control" name="dokumentasi" accept="image/*,video/*" required>
                <small class="help-block">Upload foto atau video dokumentasi patroli (Max: 5MB)</small>
              </div>
            </div>

            <div class="form-group">
              <label for="status" class="col-sm-2 control-label">Status</label>
              <div class="col-sm-10">
                <select class="form-control" name="status" required>
                  <option value="pending">Menunggu</option>
                  <option value="approved">Disetujui</option>
                  <option value="rejected">Ditolak</option>
                  <option value="completed">Selesai</option>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label for="rating" class="col-sm-2 control-label">Rating</label>
              <div class="col-sm-10">
                <select class="form-control" name="rating" id="rating-select">
                  <option value="">-- Belum Dinilai --</option>
                  <option value="1">⭐ 1 - Sangat Buruk</option>
                  <option value="2">⭐⭐ 2 - Buruk</option>
                  <option value="3">⭐⭐⭐ 3 - Cukup</option>
                  <option value="4">⭐⭐⭐⭐ 4 - Baik</option>
                  <option value="5">⭐⭐⭐⭐⭐ 5 - Sangat Baik</option>
                </select>
                <div class="rating-preview mt-2" style="display: none;">
                  <span class="rating-stars"></span>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="komentar" class="col-sm-2 control-label">Komentar</label>
              <div class="col-sm-10">
                <textarea class="form-control" name="komentar" rows="3" placeholder="Komentar opsional..."></textarea>
              </div>
            </div>

          </div>
          <div class="box-footer">
            <a href="./'.$mod.'" class="btn btn-default">Batal</a>
            <button type="submit" class="btn btn-primary pull-right">Simpan</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>';
break;

case 'edit':
$patroli_id = $_GET['patroli_id'];
$query_edit = "SELECT * FROM patroli WHERE patroli_id='$patroli_id'";
$result_edit = $connection->query($query_edit);
$row_edit = $result_edit->fetch_assoc();

echo'
<section class="content-header">
  <h1>Edit<small> Data Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li><a href="./'.$mod.'">Data Patroli</a></li>
      <li class="active">Edit Data</li>
    </ol>
</section>

<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-primary">
        <div class="box-header with-border">
          <h3 class="box-title">Form Edit Patroli</h3>
        </div>
        <form class="form-horizontal" method="POST" action="sw-mod/'.$mod.'/proses.php?action=edit" enctype="multipart/form-data">
          <input type="hidden" name="patroli_id" value="'.$row_edit['patroli_id'].'">
          <input type="hidden" name="dokumentasi_lama" value="'.$row_edit['dokumentasi'].'">
          <div class="box-body">

            <div class="form-group">
              <label for="employees_id" class="col-sm-2 control-label">Karyawan</label>
              <div class="col-sm-10">
                <select class="form-control" name="employees_id" required>
                  <option value="">-- Pilih Karyawan --</option>';
                  $query_emp = "SELECT id, employees_name FROM employees ORDER BY employees_name";
                  $result_emp = $connection->query($query_emp);
                  while($row_emp = $result_emp->fetch_assoc()) {
                    $selected = ($row_emp['id'] == $row_edit['employees_id']) ? 'selected' : '';
                    echo '<option value="'.$row_emp['id'].'" '.$selected.'>'.$row_emp['employees_name'].'</option>';
                  }
                echo'</select>
              </div>
            </div>

            <div class="form-group">
              <label for="building_id" class="col-sm-2 control-label">Lokasi</label>
              <div class="col-sm-10">
                <select class="form-control" name="building_id" required>
                  <option value="">-- Pilih Lokasi --</option>';
                  $query_building = "SELECT building_id, name FROM building ORDER BY name";
                  $result_building = $connection->query($query_building);
                  while($row_building = $result_building->fetch_assoc()) {
                    $selected = ($row_building['building_id'] == $row_edit['building_id']) ? 'selected' : '';
                    echo '<option value="'.$row_building['building_id'].'" '.$selected.'>'.$row_building['name'].'</option>';
                  }
                echo'</select>
              </div>
            </div>

            <div class="form-group">
              <label for="dokumentasi" class="col-sm-2 control-label">Dokumentasi</label>
              <div class="col-sm-10">';
                if(!empty($row_edit['dokumentasi'])) {
                  echo '<p>File saat ini: <a href="../sw-content/patroli/'.$row_edit['dokumentasi'].'" target="_blank">'.$row_edit['dokumentasi'].'</a></p>';
                }
                echo'<input type="file" class="form-control" name="dokumentasi" accept="image/*,video/*">
                <small class="help-block">Kosongkan jika tidak ingin mengubah file (Max: 5MB)</small>
              </div>
            </div>

            <div class="form-group">
              <label for="status" class="col-sm-2 control-label">Status</label>
              <div class="col-sm-10">
                <select class="form-control" name="status" required>';
                  $statuses = [
                    'pending' => 'Menunggu',
                    'approved' => 'Disetujui',
                    'rejected' => 'Ditolak',
                    'completed' => 'Selesai'
                  ];
                  foreach($statuses as $value => $label) {
                    $selected = ($value == $row_edit['status']) ? 'selected' : '';
                    echo '<option value="'.$value.'" '.$selected.'>'.$label.'</option>';
                  }
                echo'</select>
              </div>
            </div>

            <div class="form-group">
              <label for="rating" class="col-sm-2 control-label">Rating</label>
              <div class="col-sm-10">
                <select class="form-control" name="rating" id="rating-select-edit">
                  <option value="">-- Belum Dinilai --</option>';
                  for($i = 1; $i <= 5; $i++) {
                    $selected = ($i == $row_edit['rating']) ? 'selected' : '';
                    $stars = str_repeat('⭐', $i);
                    $label = '';
                    switch($i) {
                      case 1: $label = 'Sangat Buruk'; break;
                      case 2: $label = 'Buruk'; break;
                      case 3: $label = 'Cukup'; break;
                      case 4: $label = 'Baik'; break;
                      case 5: $label = 'Sangat Baik'; break;
                    }
                    echo '<option value="'.$i.'" '.$selected.'>'.$stars.' '.$i.' - '.$label.'</option>';
                  }
                echo'</select>
                <div class="rating-preview-edit mt-2" style="display: none;">
                  <span class="rating-stars"></span>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="komentar" class="col-sm-2 control-label">Komentar</label>
              <div class="col-sm-10">
                <textarea class="form-control" name="komentar" rows="3" placeholder="Komentar opsional...">'.$row_edit['komentar'].'</textarea>
              </div>
            </div>

          </div>
          <div class="box-footer">
            <a href="./'.$mod.'" class="btn btn-default">Batal</a>
            <button type="submit" class="btn btn-primary pull-right">Update</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>';
break;

case 'delete':
$patroli_id = $_GET['patroli_id'];
// Get file name to delete
$query_file = "SELECT dokumentasi FROM patroli WHERE patroli_id='$patroli_id'";
$result_file = $connection->query($query_file);
$row_file = $result_file->fetch_assoc();

// Delete file if exists
if(!empty($row_file['dokumentasi'])) {
  $file_path = '../sw-content/patroli/'.$row_file['dokumentasi'];
  if(file_exists($file_path)) {
    unlink($file_path);
  }
}

// Delete record
$query_delete = "DELETE FROM patroli WHERE patroli_id='$patroli_id'";
if($connection->query($query_delete)) {
  echo '<script>alert("Data berhasil dihapus!"); window.location="./'.$mod.'";</script>';
} else {
  echo '<script>alert("Gagal menghapus data!"); window.location="./'.$mod.'";</script>';
}
break;

}
echo'</div>';

// Include CSS dan JavaScript untuk patroli
echo '
<style>
.rating-display {
    line-height: 1.2;
}
.rating-display .fa-star,
.rating-display .fa-star-o {
    font-size: 14px;
    margin-right: 2px;
}
.rating-preview,
.rating-preview-edit {
    margin-top: 10px;
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #ddd;
}
.rating-preview .fa-star,
.rating-preview .fa-star-o,
.rating-preview-edit .fa-star,
.rating-preview-edit .fa-star-o {
    margin-right: 3px;
}
.btn-group .btn {
    margin-right: 2px;
}
.table td {
    vertical-align: middle;
}

/* Highlight styles for new/updated data */
tr.success {
    background-color: #d4edda !important;
    border: 2px solid #28a745;
}

.highlight-blink {
    animation: blink-highlight 0.5s ease-in-out 3;
}

@keyframes blink-highlight {
    0% { background-color: #d4edda; }
    50% { background-color: #c3e6cb; }
    100% { background-color: #d4edda; }
}

/* Success alert styling */
.alert-success {
    border-left: 4px solid #28a745;
}
.alert-info {
    border-left: 4px solid #17a2b8;
}
.alert-warning {
    border-left: 4px solid #ffc107;
}

/* Smooth transitions */
.table tr {
    transition: background-color 0.3s ease;
}

/* Loading indicator */
.loading {
    opacity: 0.6;
    pointer-events: none;
}
</style>
<script src="sw-mod/patroli/scripts.js"></script>
<script>
// Function to create patroli table
function createPatroliTable() {
    if(confirm("Yakin ingin membuat tabel patroli?\\nTabel akan dibuat dengan struktur yang diperlukan.")) {
        // Show loading
        $("body").addClass("loading");

        // AJAX request to create table
        $.ajax({
            url: "sw-mod/patroli/create_table_ajax.php",
            type: "POST",
            dataType: "json",
            success: function(response) {
                $("body").removeClass("loading");
                if(response.success) {
                    alert("✅ " + response.message);
                    window.location.reload();
                } else {
                    alert("❌ " + response.message);
                }
            },
            error: function() {
                $("body").removeClass("loading");
                alert("❌ Terjadi kesalahan saat membuat tabel!");
            }
        });
    }
}

// Function to add sample data
function addSampleData() {
    if(confirm("Yakin ingin menambah data contoh patroli?\\nAkan ditambahkan beberapa data contoh untuk testing.")) {
        // Show loading
        $("body").addClass("loading");

        // AJAX request to add sample data
        $.ajax({
            url: "sw-mod/patroli/add_sample_data.php",
            type: "POST",
            dataType: "json",
            success: function(response) {
                $("body").removeClass("loading");
                if(response.success) {
                    alert("✅ " + response.message);
                    window.location.reload();
                } else {
                    alert("❌ " + response.message);
                }
            },
            error: function() {
                $("body").removeClass("loading");
                alert("❌ Terjadi kesalahan saat menambah data contoh!");
            }
        });
    }
}
</script>';

include_once 'sw-mod/sw-footer.php';
}
?>
